{"name": "sui-faucet-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@suiet/wallet-kit": "^0.5.1", "@tailwindcss/vite": "^4.1.11", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}