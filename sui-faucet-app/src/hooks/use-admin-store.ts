import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AdminAPI, type AdminLoginData } from '../lib/api';

interface AdminState {
    isAuthenticated: boolean;
    token: string | null;
    user: AdminLoginData['user'] | null;
    isLoading: boolean;
    error: string | null;

    // Actions
    login: (username: string, password: string) => Promise<boolean>;
    logout: () => void;
    clearError: () => void;
}

export const useAdminStore = create<AdminState>()(
    persist(
        (set, _get) => ({
            isAuthenticated: false,
            token: null,
            user: null,
            isLoading: false,
            error: null,

            login: async (username: string, password: string) => {
                set({ isLoading: true, error: null });

                try {
                    const response = await AdminAPI.login(username, password);

                    if (response.success && response.data) {
                        set({
                            isAuthenticated: true,
                            token: response.data.token,
                            user: response.data.user,
                            isLoading: false,
                            error: null,
                        });
                        return true;
                    } else {
                        set({
                            isLoading: false,
                            error: response.message || 'Login failed',
                        });
                        return false;
                    }
                } catch (error) {
                    set({
                        isLoading: false,
                        error: error instanceof Error ? error.message : 'Login failed',
                    });
                    return false;
                }
            },

            logout: () => {
                set({
                    isAuthenticated: false,
                    token: null,
                    user: null,
                    error: null,
                });
            },

            clearError: () => {
                set({ error: null });
            },
        }),
        {
            name: 'admin-storage',
            partialize: (state) => ({
                isAuthenticated: state.isAuthenticated,
                token: state.token,
                user: state.user,
            }),
        }
    )
); 