import { create } from "zustand";
import type { ReactNode } from "react";

interface DialogState {
    isOpen: boolean;
    component: ReactNode | null;
    open: (component?: ReactNode) => void;
    close: () => void;
    setComponent: (component: ReactNode) => void;
}

export const useDialogStore = create<DialogState>((set) => ({
    isOpen: false,
    component: null,
    open: (component = null) => set({ isOpen: true, component }),
    close: () => set({ isOpen: false, component: null }),
    setComponent: (component) => set({ component }),
}));