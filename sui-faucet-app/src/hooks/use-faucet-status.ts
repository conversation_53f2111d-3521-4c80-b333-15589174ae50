import { useState, useEffect } from 'react';
import { FaucetAPI, type FaucetStatusData, type HealthData } from '../lib/api';

interface UseFaucetStatusReturn {
    status: FaucetStatusData | null;
    health: HealthData | null;
    isLoading: boolean;
    error: string | null;
    refetch: () => void;
}

export const useFaucetStatus = (): UseFaucetStatusReturn => {
    const [status, setStatus] = useState<FaucetStatusData | null>(null);
    const [health, setHealth] = useState<HealthData | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);

        try {
            // Fetch both status and health in parallel
            const [statusResponse, healthResponse] = await Promise.all([
                FaucetAPI.getFaucetStatus(),
                FaucetAPI.checkHealth(),
            ]);

            if (statusResponse.success && statusResponse.data) {
                setStatus(statusResponse.data);
            } else {
                setError('Failed to fetch faucet status');
            }

            setHealth(healthResponse);
        } catch (err) {
            console.error('Failed to fetch faucet data:', err);
            setError('Failed to connect to faucet API');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    return {
        status,
        health,
        isLoading,
        error,
        refetch: fetchData,
    };
}; 