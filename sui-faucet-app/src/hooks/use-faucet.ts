import { useState, useCallback } from 'react';
import { FaucetAPI, type ApiResponse, type FaucetRequestData, getErrorMessage, isValidSuiAddress } from '../lib/api';
import { toast } from 'sonner';

interface UseFaucetReturn {
    isLoading: boolean;
    requestTokens: (address: string) => Promise<void>;
    lastTransaction: FaucetRequestData | null;
}

export const useFaucet = (): UseFaucetReturn => {
    const [isLoading, setIsLoading] = useState(false);
    const [lastTransaction, setLastTransaction] = useState<FaucetRequestData | null>(null);

    const requestTokens = useCallback(async (address: string) => {
        if (!address.trim()) {
            toast.error('Please enter a wallet address');
            return;
        }

        if (!isValidSuiAddress(address)) {
            toast.error('Invalid Sui address format. Address must be 64 hex characters with 0x prefix');
            return;
        }

        setIsLoading(true);

        try {
            const response: ApiResponse<FaucetRequestData> = await FaucetAPI.requestTokens(address);

            if (response.success && response.data) {
                setLastTransaction(response.data);
                toast.success(response.message, {
                    action: {
                        label: 'View on Explorer',
                        onClick: () => window.open(response.data!.explorerUrl, '_blank'),
                    },
                    duration: 10000,
                });
            } else {
                const errorMessage = getErrorMessage(response);
                toast.error(errorMessage);
            }
        } catch (error) {
            console.error('Faucet request failed:', error);
            toast.error('Failed to connect to faucet API. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    }, []);

    return {
        isLoading,
        requestTokens,
        lastTransaction,
    };
}; 