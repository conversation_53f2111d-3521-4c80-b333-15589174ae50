@import "tailwindcss";

body {
  font-family: "Figtree", sans-serif;
  color: #f7f7f8;
}


:root {
  --primary-color: #4DA2FF;
}

/* Video optimization for better performance */
video {
  /* Hardware acceleration */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;

  /* Optimize rendering */
  will-change: transform;

  /* Prevent video from being selectable */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* Prevent dragging */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  video {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize for mobile devices */
@media (max-width: 768px) {

  /* Reduce animations on mobile for better performance */
  * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }
}

/* Loading state optimization */
.video-loading {
  background: linear-gradient(135deg, #001122 0%, #0066cc 100%);
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(77, 162, 255, 0.5);
  border-radius: 4px;
  transition: opacity 0.15s ease;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}

::-webkit-scrollbar-thumb:active {
  opacity: 1;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(77, 162, 255, 0.4) transparent;
}

/* Dialog animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }

  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}