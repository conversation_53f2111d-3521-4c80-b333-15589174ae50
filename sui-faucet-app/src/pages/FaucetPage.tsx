import bgVideo from "../assets/video-background.mp4";
import logo from "../assets/Sui_Faucet_Logo_White.svg";
import {
  VideoBackground,
  ConnectWalletDialog,
  WalletInfoDialog,
  FaucetStatus,
  TransactionResult,
} from "../components";
import { Button } from "../components/ui";
import { useDialogStore, useFaucet } from "../hooks";
import { useWallet } from "@suiet/wallet-kit";
import { truncateAddress } from "../lib/utils";
import { useEffect, useState } from "react";

export function FaucetPage() {
  const { open } = useDialogStore();
  const { account, connected } = useWallet();
  const { isLoading, requestTokens, lastTransaction } = useFaucet();
  const [address, setAddress] = useState<string>("");

  useEffect(() => {
    if (account) {
      setAddress(account.address);
    }
  }, [account]);

  function renderAccountInfo(): string | null {
    if (!connected) return null;
    if (account?.suinsName) {
      return account.suinsName;
    } else {
      return truncateAddress(account?.address || null);
    }
  }

  const handleRequestTokens = async () => {
    await requestTokens(address);
  };

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAddress(e.target.value);
  };

  return (
    <main className="min-h-screen relative flex flex-col">
      {/* Video Background */}
      <div className="absolute inset-0">
        <VideoBackground src={bgVideo} />
      </div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/50"></div>

      {/* Header */}
      <header className="relative z-10">
        <div className="container mx-auto px-4 py-6 flex justify-between items-center">
          <img src={logo} alt="Sui Faucet" className="h-8 md:h-12" />
          {renderAccountInfo() ? (
            <Button onClick={() => open(<WalletInfoDialog />)}>
              {renderAccountInfo()}
            </Button>
          ) : (
            <Button onClick={() => open(<ConnectWalletDialog />)}>
              Connect
            </Button>
          )}
        </div>
      </header>

      {/* Content */}
      <div className="px-4 py-6 flex-1 flex items-center justify-center">
        <div className="text-center relative z-10 w-full max-w-md">
          <p className="text-md text-[#f7f7f8]/80 mb-4 mx-auto">
            Get testnet SUI tokens for development and testing purposes
          </p>

          {/* Faucet Status */}
          <FaucetStatus />

          {/* Transaction Result */}
          {lastTransaction && (
            <TransactionResult transaction={lastTransaction} />
          )}

          {/* Faucet Form */}
          <div
            className="bg-white/10 backdrop-blur-[30px] rounded-[30px] p-6 md:p-8 border border-white/20"
            style={{
              background:
                "linear-gradient(to left, #FFFFFF33, #FFFFFF0F, #FFFFFF0D, #FFFFFF1C)",
            }}
          >
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-[#f7f7f8] mb-2">
                  Wallet Address
                </label>
                <input
                  type="text"
                  placeholder="Your Sui address (0x...)"
                  value={address}
                  onChange={handleAddressChange}
                  className="w-full px-4 py-3 rounded-[30px] bg-white/20 border border-white/30 text-[#f7f7f8] placeholder-white/50 focus:outline-none focus:border-[#4DA2FF] transition-colors"
                  disabled={isLoading}
                />
              </div>

              <Button
                size="md"
                className="w-full"
                onClick={handleRequestTokens}
                disabled={isLoading || !address.trim()}
              >
                {isLoading ? "Requesting..." : "Request SUI"}
              </Button>

              {!connected && (
                <p className="text-xs text-white/60 mt-2">
                  Tip: Connect your wallet to auto-fill your address
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative z-10">
        <div className="container mx-auto px-4 py-6 text-xs md:text-sm">
          <p className="text-white/80 mb-1">
            This tool is designed for development purposes and does not
            distribute mainnet SUI.
          </p>
          <p className="text-white/60">
            © {new Date().getFullYear()} Sui Foundation. All rights reserved.
          </p>
        </div>
      </footer>
    </main>
  );
}
