// API Configuration
const BASE_URL = import.meta.env.VITE_FAUCET_API_BASE_URL;
const API_KEY = import.meta.env.VITE_FAUCET_API_KEY;

// Validate required environment variables
if (!BASE_URL) {
    throw new Error('VITE_FAUCET_API_BASE_URL is required in .env file');
}
if (!API_KEY) {
    throw new Error('VITE_FAUCET_API_KEY is required in .env file');
}

// API Response Types
export interface ApiResponse<T = unknown> {
    success: boolean;
    message: string;
    data?: T;
    error?: {
        code: string;
        details: string;
    };
    retryAfter?: number;
    timestamp: string;
}

export interface FaucetRequestData {
    transactionHash: string;
    amount: string;
    amountInSui: number;
    walletAddress: string;
    network: string;
    explorerUrl: string;
}

export interface FaucetStatusData {
    balanceInSui: number;
    network: string;
    defaultAmountInSui: number;
    isOperational: boolean;
    rateLimits: {
        perWallet: string;
        perIP: string;
        global: string;
    };
}

export interface HealthData {
    status: string;
    timestamp: string;
    uptime: string;
    responseTime: string;
    services: {
        database: string;
        redis: string;
        sui: string;
    };
}

// API Functions
export class FaucetAPI {
    private static async makeRequest<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        try {
            const response = await fetch(`${BASE_URL}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': API_KEY,
                    ...options.headers,
                },
                ...options,
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw new Error('Failed to connect to faucet API');
        }
    }

    // Request SUI tokens
    static async requestTokens(address: string): Promise<ApiResponse<FaucetRequestData>> {
        return this.makeRequest<FaucetRequestData>('/api/v1/faucet/request', {
            method: 'POST',
            body: JSON.stringify({ address }),
        });
    }

    // Get faucet status
    static async getFaucetStatus(): Promise<ApiResponse<FaucetStatusData>> {
        return this.makeRequest<FaucetStatusData>('/api/v1/faucet/status');
    }

    // Check API health
    static async checkHealth(): Promise<HealthData> {
        const response = await fetch(`${BASE_URL}/api/v1/health`);
        return response.json();
    }

    // Get API information
    static async getApiInfo(): Promise<ApiResponse> {
        const response = await fetch(`${BASE_URL}/`);
        return response.json();
    }
}

// Utility functions for error handling
export const getErrorMessage = (error: ApiResponse): string => {
    if (error.error?.code === 'RATE_LIMIT_EXCEEDED') {
        const hours = Math.floor((error.retryAfter || 0) / 3600);
        const minutes = Math.floor(((error.retryAfter || 0) % 3600) / 60);
        const seconds = (error.retryAfter || 0) % 60;

        let timeString = '';
        if (hours > 0) timeString += `${hours}h `;
        if (minutes > 0) timeString += `${minutes}m `;
        if (seconds > 0) timeString += `${seconds}s`;

        return `Rate limit exceeded. Please try again in ${timeString.trim()}.`;
    }

    return error.message || 'An unexpected error occurred';
};

export const isValidSuiAddress = (address: string): boolean => {
    // Basic validation for Sui address format
    const suiAddressRegex = /^0x[a-fA-F0-9]{64}$/;
    return suiAddressRegex.test(address);
};

// Admin API Types
export interface AdminLoginData {
    token: string;
    expiresIn: string;
    user: {
        username: string;
        role: string;
    };
}

export interface AdminDashboardData {
    systemStats: {
        totalRequests: number;
        successfulRequests: number;
        failedRequests: number;
        uniqueUsers: number;
    };
    faucetInfo: {
        balance: string;
        network: string;
        isOperational: boolean;
    };
    recentActivity: Array<{
        timestamp: string;
        action: string;
        wallet: string;
        amount: string;
        status: string;
    }>;
}

export interface AdminStatsData {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    uniqueUsers: number;
    totalAmountDistributed: string;
    dailyBreakdown: Array<{
        date: string;
        requests: number;
        successful: number;
        failed: number;
        amount: string;
    }>;
    topUsers: Array<{
        wallet: string;
        requests: number;
        totalAmount: string;
    }>;
}

export interface AdminTestTransactionData {
    transactionHash: string;
    amount: string;
    amountInSui: number;
    walletAddress: string;
    explorerUrl: string;
}

// Admin API Functions
export class AdminAPI {
    private static async makeAuthenticatedRequest<T>(
        endpoint: string,
        token: string,
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        try {
            const response = await fetch(`${BASE_URL}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                    ...options.headers,
                },
                ...options,
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Admin API request failed:', error);
            throw new Error('Failed to connect to admin API');
        }
    }

    // Admin login
    static async login(username: string, password: string): Promise<ApiResponse<AdminLoginData>> {
        try {
            const response = await fetch(`${BASE_URL}/api/v1/admin/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Admin login failed:', error);
            throw new Error('Failed to connect to admin API');
        }
    }

    // Get admin dashboard data
    static async getDashboard(token: string): Promise<ApiResponse<AdminDashboardData>> {
        return this.makeAuthenticatedRequest<AdminDashboardData>('/api/v1/admin/dashboard', token);
    }

    // Get faucet statistics
    static async getStats(token: string, days: number = 7): Promise<ApiResponse<AdminStatsData>> {
        return this.makeAuthenticatedRequest<AdminStatsData>(`/api/v1/admin/faucet/stats?days=${days}`, token);
    }

    // Send test transaction
    static async testTransaction(
        token: string,
        walletAddress: string,
        amount: string
    ): Promise<ApiResponse<AdminTestTransactionData>> {
        return this.makeAuthenticatedRequest<AdminTestTransactionData>('/api/v1/admin/faucet/test', token, {
            method: 'POST',
            body: JSON.stringify({ walletAddress, amount }),
        });
    }
} 