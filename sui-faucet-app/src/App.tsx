import { BrowserRouter as Router, Routes, Route } from "react-router";
import { Toaster } from "sonner";
import { FaucetPage } from "./pages/FaucetPage";
import {
  AdminLogin,
  AdminLayout,
  AdminDashboard,
  ProtectedRoute,
} from "./components/admin";

function App() {
  return (
    <Router>
      <Routes>
        {/* Public faucet page */}
        <Route path="/" element={<FaucetPage />} />

        {/* Admin routes (hidden) */}
        <Route path="/admin/login" element={<AdminLogin />} />
        <Route
          path="/admin"
          element={
            <ProtectedRoute>
              <AdminLayout />
            </ProtectedRoute>
          }
        >
          <Route path="dashboard" element={<AdminDashboard />} />
          <Route
            path="settings"
            element={
              <div className="text-center text-gray-500">
                Settings page coming soon...
              </div>
            }
          />
          <Route index element={<AdminDashboard />} />
        </Route>
      </Routes>

      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: "rgba(255, 255, 255, 0.95)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          },
        }}
      />
    </Router>
  );
}

export default App;
