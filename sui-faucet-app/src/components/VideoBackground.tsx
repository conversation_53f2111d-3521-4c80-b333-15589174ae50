import { useState, useEffect, useRef, useCallback } from "react";

interface VideoBackgroundProps {
  src: string;
  className?: string;
}

export function VideoBackground({ src, className = "" }: VideoBackgroundProps) {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [isVideoError, setIsVideoError] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isIntersecting, setIsIntersecting] = useState(true);

  // Detect mobile and slow connections
  const checkDeviceAndConnection = useCallback(() => {
    const isMobileDevice =
      window.innerWidth < 768 ||
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );

    setIsMobile(isMobileDevice);

    // Check for slow connection
    const connection = (
      navigator as Navigator & {
        connection?: {
          effectiveType?: string;
          saveData?: boolean;
        };
      }
    ).connection;
    const isSlowConnection =
      connection &&
      (connection.effectiveType === "slow-2g" ||
        connection.effectiveType === "2g" ||
        connection.saveData === true);

    return { isMobileDevice, isSlowConnection };
  }, []);

  // Intersection Observer for performance
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    const videoElement = videoRef.current;
    if (videoElement) {
      observer.observe(videoElement);
    }

    return () => {
      if (videoElement) {
        observer.unobserve(videoElement);
      }
    };
  }, []);

  useEffect(() => {
    const { isMobileDevice, isSlowConnection } = checkDeviceAndConnection();

    // Don't load video on mobile or slow connections
    if (isMobileDevice || isSlowConnection) {
      return;
    }

    const video = videoRef.current;
    if (!video || !isIntersecting) return;

    const handleCanPlay = () => {
      setIsVideoLoaded(true);
      setIsVideoError(false);
    };

    const handleError = () => {
      setIsVideoError(true);
      setIsVideoLoaded(false);
    };

    const handleLoadStart = () => {
      setIsVideoLoaded(false);
      setIsVideoError(false);
    };

    video.addEventListener("canplaythrough", handleCanPlay);
    video.addEventListener("error", handleError);
    video.addEventListener("loadstart", handleLoadStart);

    // Preload only metadata first
    video.preload = "metadata";
    video.load();

    // Handle window resize
    const handleResize = () => {
      const { isMobileDevice: newIsMobile } = checkDeviceAndConnection();
      if (newIsMobile && !isMobile) {
        video.pause();
        video.removeAttribute("src");
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      video.removeEventListener("canplaythrough", handleCanPlay);
      video.removeEventListener("error", handleError);
      video.removeEventListener("loadstart", handleLoadStart);
      window.removeEventListener("resize", handleResize);
    };
  }, [checkDeviceAndConnection, isMobile, isIntersecting]);

  // Pause video when not visible for performance
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isIntersecting && isVideoLoaded) {
      video.play().catch(() => {
        // Handle autoplay restrictions
        setIsVideoError(true);
      });
    } else {
      video.pause();
    }
  }, [isIntersecting, isVideoLoaded]);

  if (isMobile || isVideoError) {
    return (
      <div
        className={`w-full h-full bg-gradient-to-br from-[#001122] to-[#0066cc] ${className}`}
      />
    );
  }

  return (
    <div className="relative w-full h-full">
      {/* Loading background */}
      <div
        className={`absolute inset-0 video-loading transition-opacity duration-500 ${
          isVideoLoaded ? "opacity-0" : "opacity-100"
        }`}
      />

      {/* Video */}
      <video
        ref={videoRef}
        src={src}
        autoPlay
        loop
        muted
        playsInline
        preload="metadata"
        className={`w-full h-full object-cover transition-opacity duration-500 ${
          isVideoLoaded ? "opacity-100" : "opacity-0"
        } ${className}`}
        poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1920 1080'%3E%3Cdefs%3E%3ClinearGradient id='bg' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23001122'/%3E%3Cstop offset='100%25' style='stop-color:%230066cc'/%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23bg)'/%3E%3C/svg%3E"
      />
    </div>
  );
}
