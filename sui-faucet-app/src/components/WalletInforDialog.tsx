import { ExternalLink, CirclePower } from "lucide-react";
import { useDialogStore } from "../hooks";
import { <PERSON>ton, DialogHeader } from "./ui";
import { useWallet } from "@suiet/wallet-kit";
import { useState, useEffect } from "react";
import { toast } from "sonner";

export const WalletInfoDialog = () => {
  const { close } = useDialogStore();
  const { account, disconnect } = useWallet();
  const [balance, setBalance] = useState<string>("0");
  const [loading, setLoading] = useState(true);

  // Fetch SUI balance
  useEffect(() => {
    const fetchBalance = async () => {
      if (!account?.address) {
        setLoading(false);
        return;
      }

      try {
        // Use Sui testnet RPC endpoint
        const response = await fetch("https://fullnode.testnet.sui.io:443", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            jsonrpc: "2.0",
            id: 1,
            method: "suix_getBalance",
            params: [account.address],
          }),
        });

        const data = await response.json();
        if (data.result) {
          // Convert from MIST (smallest unit) to SUI
          const balanceInSui = (
            parseInt(data.result.totalBalance) / **********
          ).toFixed(6);
          setBalance(balanceInSui);
        }
      } catch (error) {
        console.error("Error fetching balance:", error);
        setBalance("Error");
      } finally {
        setLoading(false);
      }
    };

    fetchBalance();
  }, [account?.address]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied to clipboard");
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  if (!account) {
    return null;
  }

  return (
    <div className="flex flex-col flex-1 min-h-0">
      <DialogHeader
        title="Wallet Information"
        onClose={close}
        className="pb-4"
      />

      <div className="flex-1 flex flex-col space-y-2">
        {/* SUI Name (if available) */}
        {account.suinsName && (
          <div className="bg-foreground/60 rounded-2xl p-4 border border-white/10">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                  SUI Name
                </p>
                <p className="text-lg font-semibold text-white">
                  {account.suinsName}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Wallet Address */}
        <div className="bg-foreground/60 rounded-2xl p-4 border border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                Wallet Address
              </p>
              <p
                className="text-sm font-mono text-white break-all cursor-pointer hover:underline"
                onClick={() => copyToClipboard(account.address)}
              >
                {account.address}
              </p>
            </div>
          </div>
        </div>

        {/* SUI Balance */}
        <div className="bg-foreground/60 rounded-2xl p-4 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                SUI Balance (Testnet)
              </p>
              <div className="flex items-center space-x-2">
                <p className="text-2xl font-bold text-white">
                  {loading ? "..." : balance}
                </p>
                <span className="text-sm text-gray-400">SUI</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-3 mb-4 md:mb-0 flex gap-2">
          <Button
            size="md"
            className="w-full flex items-center justify-center gap-2"
            onClick={() =>
              window.open(
                `https://suiscan.xyz/testnet/address/${account.address}`,
                "_blank"
              )
            }
          >
            View on Suiscan <ExternalLink className="size-5 " />
          </Button>
          <Button
            className="w-fit flex items-center justify-center gap-2 bg-white/20 hover:bg-red-500/70"
            onClick={() => {
              disconnect();
              close();
            }}
          >
            Disconnect <CirclePower className="size-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};
