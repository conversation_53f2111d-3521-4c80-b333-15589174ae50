import { useFaucetStatus } from "../hooks";
import { RefreshCw, AlertCircle, CheckCircle } from "lucide-react";

export const FaucetStatus = () => {
  const { status, health, isLoading, error, refetch } = useFaucetStatus();

  if (isLoading) {
    return (
      <div className="bg-white/10 backdrop-blur-[30px] rounded-2xl p-4 border border-white/20 mb-4">
        <div className="flex items-center justify-start space-x-2">
          <RefreshCw className="size-4 animate-spin text-white/60" />
          <span className="text-sm text-white/60">
            Loading faucet status...
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/10 backdrop-blur-[30px] rounded-2xl p-4 border border-white/20 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <AlertCircle className="size-4 text-red-400" />
            <span className="text-sm text-red-400">{error}</span>
          </div>
          <button
            onClick={refetch}
            className="p-1 rounded-lg hover:bg-white/10 transition-colors"
          >
            <RefreshCw className="size-4 text-white/60" />
          </button>
        </div>
      </div>
    );
  }

  if (!status) return null;

  return (
    <div className="bg-white/10 backdrop-blur-[30px] rounded-2xl p-4 border border-white/20 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-white">Faucet Status</h3>
        <button
          onClick={refetch}
          className="p-1 rounded-lg hover:bg-white/10 transition-colors"
        >
          <RefreshCw className="size-4 text-white/60" />
        </button>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* Operational Status */}
        <div className="flex items-center space-x-2">
          {status.isOperational ? (
            <CheckCircle className="size-4 text-green-400" />
          ) : (
            <AlertCircle className="size-4 text-red-400" />
          )}
          <span className="text-xs text-white/80">
            {status.isOperational ? "Operational" : "Offline"}
          </span>
        </div>

        {/* Balance */}
        <div className="text-right">
          <div className="text-xs text-white/60">Balance</div>
          <div className="text-sm font-semibold text-white">
            {status.balanceInSui.toFixed(2)} SUI
          </div>
        </div>

        {/* Network */}
        <div>
          <div className="text-xs text-white/60">Network</div>
          <div className="text-sm font-medium text-white capitalize">
            {status.network}
          </div>
        </div>

        {/* Default Amount */}
        <div className="text-right">
          <div className="text-xs text-white/60">Amount per request</div>
          <div className="text-sm font-medium text-white">
            {status.defaultAmountInSui} SUI
          </div>
        </div>
      </div>

      {/* Rate Limits */}
      <div className="mt-3 pt-3 border-t border-white/10">
        <div className="text-xs text-white/60 mb-2">Rate Limits</div>
        <div className="grid grid-cols-1 gap-1 text-xs text-white/80">
          <div>Per wallet: {status.rateLimits.perWallet}</div>
          <div>Per IP: {status.rateLimits.perIP}</div>
        </div>
      </div>

      {/* Health Status */}
      {health && (
        <div className="mt-3 pt-3 border-t border-white/10">
          <div className="flex items-center justify-between">
            <span className="text-xs text-white/60">API Health</span>
            <span
              className={`text-xs font-medium ${
                health.status === "healthy" ? "text-green-400" : "text-red-400"
              }`}
            >
              {health.status}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
