import { ExternalLink, CheckCircle, Copy } from "lucide-react";
import type { FaucetRequestData } from "../lib/api";
import { truncateAddress } from "../lib/utils";
import { toast } from "sonner";

interface TransactionResultProps {
  transaction: FaucetRequestData;
  onClose?: () => void;
}

export const TransactionResult = ({
  transaction,
  onClose,
}: TransactionResultProps) => {
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${label} copied to clipboard`);
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy to clipboard");
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-[30px] rounded-2xl p-6 border border-white/20 mb-4">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-2">
          <CheckCircle className="size-5 text-green-400" />
          <h3 className="text-sm font-semibold text-white">
            Transaction Successful
          </h3>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white/80 text-xl leading-none"
          >
            ×
          </button>
        )}
      </div>

      <div className="space-y-3">
        {/* Amount */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-white/60">Amount Received</span>
          <span className="text-sm font-semibold text-green-400">
            {transaction.amountInSui} SUI
          </span>
        </div>

        {/* Wallet Address */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-white/60">Wallet</span>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-mono text-white">
              {truncateAddress(transaction.walletAddress, 6)}
            </span>
            <button
              onClick={() =>
                copyToClipboard(transaction.walletAddress, "Address")
              }
              className="p-1 rounded hover:bg-white/10 transition-colors"
            >
              <Copy className="size-3 text-white/60" />
            </button>
          </div>
        </div>

        {/* Transaction Hash */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-white/60">Transaction</span>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-mono text-white">
              {truncateAddress(transaction.transactionHash, 6)}
            </span>
            <button
              onClick={() =>
                copyToClipboard(transaction.transactionHash, "Transaction hash")
              }
              className="p-1 rounded hover:bg-white/10 transition-colors"
            >
              <Copy className="size-3 text-white/60" />
            </button>
          </div>
        </div>

        {/* Network */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-white/60">Network</span>
          <span className="text-sm text-white capitalize">
            {transaction.network}
          </span>
        </div>
      </div>

      {/* Action Button */}
      <div className="mt-4 pt-4 border-t border-white/10">
        <button
          onClick={() => window.open(transaction.explorerUrl, "_blank")}
          className="w-full flex items-center justify-center space-x-2 py-2 px-4 bg-white/10 hover:bg-white/20 rounded-lg transition-colors text-sm text-white"
        >
          <span>View on Explorer</span>
          <ExternalLink className="size-4" />
        </button>
      </div>
    </div>
  );
};
