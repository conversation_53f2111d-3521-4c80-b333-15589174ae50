import { useState } from "react";
import { Navigate } from "react-router";
import { useAdminStore } from "../../hooks/use-admin-store";
import { toast } from "sonner";

export function AdminLogin() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const { login, isLoading, error, isAuthenticated, clearError } =
    useAdminStore();

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/admin/dashboard" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!username.trim() || !password.trim()) {
      toast.error("Please enter both username and password");
      return;
    }

    const success = await login(username, password);
    if (success) {
      toast.success("Login successful!");
    } else {
      toast.error(error || "Login failed");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white/10 backdrop-blur-[30px] rounded-[30px] p-8 border border-white/20">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-white mb-2">Admin Panel</h1>
            <p className="text-white/70">
              Sign in to access the faucet admin dashboard
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Username
              </label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-4 py-3 rounded-[20px] bg-white/20 border border-white/30 text-white placeholder-white/50 focus:outline-none focus:border-blue-400 transition-colors"
                placeholder="Enter your username"
                disabled={isLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 rounded-[20px] bg-white/20 border border-white/30 text-white placeholder-white/50 focus:outline-none focus:border-blue-400 transition-colors"
                placeholder="Enter your password"
                disabled={isLoading}
              />
            </div>

            {error && (
              <div className="bg-red-500/20 border border-red-500/30 rounded-[20px] p-3">
                <p className="text-red-200 text-sm text-center">{error}</p>
              </div>
            )}

            <button
              type="submit"
              className="w-full py-4 px-[18px] bg-[#4DA2FF] hover:bg-[#4DA2FF]/80 transition-all duration-300 text-[#f7f7f8] font-medium rounded-[36px] text-[15px] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-[#4DA2FF]"
              disabled={isLoading || !username.trim() || !password.trim()}
            >
              {isLoading ? "Signing in..." : "Sign In"}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-white/50 text-xs">Authorized personnel only</p>
          </div>
        </div>
      </div>
    </div>
  );
}
