import { useEffect, useState } from "react";
import {
  AdminAPI,
  type AdminDashboardData,
  type AdminStatsData,
} from "../../lib/api";
import { useAdminStore } from "../../hooks/use-admin-store";
import { Button } from "../ui";
import {
  Users,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Send,
  Activity,
} from "lucide-react";
import { toast } from "sonner";

export function AdminDashboard() {
  const { token } = useAdminStore();
  const [dashboardData, setDashboardData] = useState<AdminDashboardData | null>(
    null
  );
  const [statsData, setStatsData] = useState<AdminStatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [testAddress, setTestAddress] = useState("");
  const [testAmount, setTestAmount] = useState("100000000"); // 0.1 SUI in MIST
  const [isTestingTransaction, setIsTestingTransaction] = useState(false);

  const fetchData = async (showToast = false) => {
    if (!token) return;

    try {
      setIsRefreshing(true);

      const [dashboardResponse, statsResponse] = await Promise.all([
        AdminAPI.getDashboard(token),
        AdminAPI.getStats(token, 7),
      ]);

      if (dashboardResponse.success && dashboardResponse.data) {
        setDashboardData(dashboardResponse.data);
      }

      if (statsResponse.success && statsResponse.data) {
        setStatsData(statsResponse.data);
      }

      if (showToast) {
        toast.success("Data refreshed successfully");
      }
    } catch (error) {
      toast.error("Failed to fetch dashboard data");
      console.error("Dashboard fetch error:", error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleTestTransaction = async () => {
    if (!token || !testAddress.trim()) {
      toast.error("Please enter a wallet address");
      return;
    }

    setIsTestingTransaction(true);
    try {
      const response = await AdminAPI.testTransaction(
        token,
        testAddress,
        testAmount
      );

      if (response.success && response.data) {
        toast.success(
          `Test transaction sent! Hash: ${response.data.transactionHash.slice(
            0,
            8
          )}...`
        );
        setTestAddress("");
        // Refresh data after test transaction
        await fetchData();
      } else {
        toast.error(response.message || "Test transaction failed");
      }
    } catch (error) {
      toast.error("Failed to send test transaction");
      console.error("Test transaction error:", error);
    } finally {
      setIsTestingTransaction(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [token]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatSUI = (amount: string | number) => {
    return `${parseFloat(amount.toString()).toFixed(4)} SUI`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <Button
          size="sm"
          className="flex items-center space-x-2"
          onClick={() => fetchData(true)}
          disabled={isRefreshing}
        >
          <RefreshCw size={16} className={isRefreshing ? "animate-spin" : ""} />
          <span>Refresh</span>
        </Button>
      </div>

      {/* System Stats Cards */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(dashboardData.systemStats.totalRequests)}
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Successful</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatNumber(dashboardData.systemStats.successfulRequests)}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatNumber(dashboardData.systemStats.failedRequests)}
                </p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Unique Users</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatNumber(dashboardData.systemStats.uniqueUsers)}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Faucet Info */}
        {dashboardData && (
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Faucet Information
            </h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Balance:</span>
                <span className="font-semibold text-blue-600">
                  {formatSUI(dashboardData.faucetInfo.balance)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Network:</span>
                <span className="font-semibold">
                  {dashboardData.faucetInfo.network}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Status:</span>
                <div className="flex items-center space-x-2">
                  {dashboardData.faucetInfo.isOperational ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 font-semibold">
                        Operational
                      </span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-red-600 font-semibold">Down</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Test Transaction */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Test Transaction
          </h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Wallet Address
              </label>
              <input
                type="text"
                value={testAddress}
                onChange={(e) => setTestAddress(e.target.value)}
                placeholder="0x..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount (MIST)
              </label>
              <input
                type="text"
                value={testAmount}
                onChange={(e) => setTestAmount(e.target.value)}
                placeholder="100000000"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">
                {formatSUI(parseInt(testAmount) / 1e9 || 0)}
              </p>
            </div>
            <Button
              size="sm"
              className="flex items-center space-x-2 w-full justify-center"
              onClick={handleTestTransaction}
              disabled={isTestingTransaction || !testAddress.trim()}
            >
              <Send size={16} />
              <span>
                {isTestingTransaction ? "Sending..." : "Send Test Transaction"}
              </span>
            </Button>
          </div>
        </div>
      </div>

      {/* Statistics Summary */}
      {statsData && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            7-Day Statistics
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {formatSUI(statsData.totalAmountDistributed)}
              </p>
              <p className="text-sm text-gray-600">Total Distributed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {(
                  (statsData.successfulRequests / statsData.totalRequests) *
                  100
                ).toFixed(1)}
                %
              </p>
              <p className="text-sm text-gray-600">Success Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {formatNumber(statsData.uniqueUsers)}
              </p>
              <p className="text-sm text-gray-600">Unique Users</p>
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity */}
      {dashboardData?.recentActivity &&
        dashboardData.recentActivity.length > 0 && (
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Activity
            </h2>
            <div className="space-y-3">
              {dashboardData.recentActivity
                .slice(0, 5)
                .map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-2 border-b last:border-b-0"
                  >
                    <div className="flex items-center space-x-3">
                      {activity.status === "success" ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm text-gray-900">
                        {activity.wallet} requested {formatSUI(activity.amount)}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {new Date(activity.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                ))}
            </div>
          </div>
        )}
    </div>
  );
}
