import { useNavigate, Outlet } from "react-router";
import { useAdminStore } from "../../hooks/use-admin-store";
import { Button } from "../ui";
import { LogOut, BarChart3, Settings, Home } from "lucide-react";
import { toast } from "sonner";

export function AdminLayout() {
  const navigate = useNavigate();
  const { user, logout } = useAdminStore();

  const handleLogout = () => {
    logout();
    toast.success("Logged out successfully");
    navigate("/admin/login");
  };

  const handleGoHome = () => {
    navigate("/");
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">
                Sui Faucet Admin
              </h1>
              <span className="text-sm text-gray-500">
                Welcome, {user?.username}
              </span>
            </div>

            <div className="flex items-center space-x-4">
              <Button
                size="sm"
                className="flex items-center space-x-2"
                onClick={handleGoHome}
              >
                <Home size={16} />
                <span>Go to Faucet</span>
              </Button>

              <Button
                size="sm"
                className="flex items-center space-x-2 bg-red-500 hover:bg-red-600"
                onClick={handleLogout}
              >
                <LogOut size={16} />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => navigate("/admin/dashboard")}
              className="flex items-center space-x-2 py-4 px-1 border-b-2 border-transparent hover:border-blue-500 text-gray-600 hover:text-blue-600 transition-colors"
            >
              <BarChart3 size={20} />
              <span>Dashboard</span>
            </button>

            <button
              onClick={() => navigate("/admin/settings")}
              className="flex items-center space-x-2 py-4 px-1 border-b-2 border-transparent hover:border-blue-500 text-gray-600 hover:text-blue-600 transition-colors"
            >
              <Settings size={20} />
              <span>Settings</span>
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Outlet />
      </main>
    </div>
  );
}
