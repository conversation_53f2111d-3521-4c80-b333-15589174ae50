import { useDialogStore } from "../../hooks";
import { X } from "lucide-react";
import { IconButton } from "./Button";
import { cn } from "../../lib/utils";
import { useEffect, useState } from "react";

export const Dialog = () => {
  const { isOpen, component, close } = useDialogStore();
  // Initialize with correct mobile state to prevent flickering
  const [isMobile, setIsMobile] = useState(() =>
    typeof window !== "undefined" ? window.innerWidth < 768 : false
  );

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Only add resize listener, don't call checkMobile again since we initialized correctly
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex md:items-center md:justify-center items-end justify-center z-[51] opacity-100 transition-opacity duration-200"
      onClick={close}
      style={{
        animation: "fadeIn 0.2s ease-out",
      }}
    >
      <div
        className={cn(
          "bg-[#030f1c] border border-white/10 px-[30px] pt-6 md:pb-6 flex flex-col rounded-t-[40px] md:rounded-[40px] max-h-[80vh] w-full md:max-w-md overflow-y-auto transition-all duration-300 ease-out"
        )}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
        style={{
          animation: isMobile
            ? "slideUpFromBottom 0.3s ease-out"
            : "scaleIn 0.3s ease-out",
        }}
      >
        {component}
      </div>
    </div>
  );
};

export const DialogHeader = ({
  title,
  onClose,
  className,
}: {
  title: string;
  onClose: () => void;
  className?: string;
}) => {
  return (
    <div className={cn("flex justify-between items-center", className)}>
      <p className="text-lg">{title}</p>
      <IconButton onClick={onClose} className="size-8">
        <X className="size-6" />
      </IconButton>
    </div>
  );
};
