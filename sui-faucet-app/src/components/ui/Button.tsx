import { cn } from "../../lib/utils";

export const Button = ({
  children,
  size = "sm",
  className,
  onClick,
  disabled = false,
}: {
  children: React.ReactNode;
  size?: "sm" | "md";
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}) => {
  return (
    <button
      className={cn(
        "bg-[#4DA2FF] hover:bg-[#4DA2FF]/80 transition-all duration-300 text-[#f7f7f8] font-medium  rounded-[36px] text-[15px] cursor-pointer",
        size === "sm" ? "px-4 py-1" : "py-4 px-[18px]",
        disabled && "opacity-50 cursor-not-allowed hover:bg-[#4DA2FF]",
        className
      )}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export const IconButton = ({
  children,
  className,
  onClick,
  disabled,
}: {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}) => {
  return (
    <button
      className={cn(
        className,
        "rounded-full hover:bg-white/10 transition-all duration-300 flex items-center justify-center cursor-pointer"
      )}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};
