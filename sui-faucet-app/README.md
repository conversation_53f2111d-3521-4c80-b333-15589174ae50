# 🌊 Sui Faucet App

A modern React application for requesting SUI testnet tokens. Built with React, TypeScript, and Vite.

## Features

- 🔗 **Wallet Integration**: Connect with various Sui wallets using @suiet/wallet-kit
- 🚰 **Token Requests**: Request testnet SUI tokens with a simple interface
- 📊 **Real-time Status**: View faucet status, balance, and health information
- 🎨 **Modern UI**: Beautiful glass-morphism design with video background
- 📱 **Responsive**: Works seamlessly on desktop and mobile devices
- ⚡ **Rate Limiting**: Built-in rate limiting with clear user feedback
- 🔍 **Transaction Tracking**: View transaction details and explorer links

## API Integration

The app integrates with the Sui Faucet API to provide:

- Token distribution to wallet addresses
- Real-time faucet status and balance
- Health monitoring of API services
- Rate limit information and error handling

### Environment Variables

Create a `.env` file in the root directory with the following configuration:

```env
# Sui Faucet API Configuration
VITE_FAUCET_API_BASE_URL=http://**************
VITE_FAUCET_API_KEY=suisuisui
```

**Note**: Both variables are required. The application will throw an error if they are not provided.

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- pnpm (recommended) or npm

### Installation

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview
```

### Development

```bash
# Run linting
pnpm lint

# Type checking
npx tsc --noEmit
```

## Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── ConnectDialog.tsx
│   ├── WalletInfoDialog.tsx
│   ├── FaucetStatus.tsx
│   └── TransactionResult.tsx
├── hooks/              # Custom React hooks
│   ├── use-faucet.ts   # Faucet request logic
│   └── use-faucet-status.ts
├── lib/                # Utilities and API
│   ├── api.ts          # API client and types
│   └── utils.ts        # Helper functions
└── assets/             # Static assets
```

## API Documentation

The app connects to the Sui Faucet API. See `API_DOCUMENTATION.md` for detailed API reference.

### Key Endpoints

- `POST /api/v1/faucet/request` - Request tokens
- `GET /api/v1/faucet/status` - Get faucet status
- `GET /api/v1/health` - Health check

## Technologies Used

- **React 19** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Styling
- **@suiet/wallet-kit** - Sui wallet integration
- **Zustand** - State management
- **Sonner** - Toast notifications
- **Lucide React** - Icons

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

---

**Note**: This tool is for development purposes only and distributes testnet SUI tokens, not mainnet tokens.
