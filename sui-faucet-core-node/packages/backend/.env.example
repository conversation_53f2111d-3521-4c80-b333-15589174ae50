NODE_ENV=development
PORT=3001
DATABASE_URL=postgresql://username:password@localhost:5432/sui_faucet
REDIS_URL=redis://localhost:6379


SUI_NETWORK=testnet
SUI_PRIVATE_KEY=your_sui_private_key_here
SUI_DEFAULT_AMOUNT=100000000
SUI_MAX_AMOUNT=500000000
SUI_MIN_WALLET_BALANCE=600000000


RATE_LIMIT_WINDOW_MS=3600000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_MAX_PER_WALLET=1
RATE_LIMIT_MAX_PER_IP=10


API_KEY=your_secure_api_key_here
ADMIN_API_KEY=your_admin_api_key_here
JWT_SECRET=your_jwt_secret_here


LOG_LEVEL=info


ENABLE_SWAGGER=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000
