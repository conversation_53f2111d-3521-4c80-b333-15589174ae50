# Prometheus Alert Rules for Sui Faucet

groups:
  - name: sui-faucet-alerts
    rules:
      # Application Health
      - alert: ApplicationDown
        expr: up{job="sui-faucet"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Sui Faucet application is down"
          description: "The Sui Faucet application has been down for more than 1 minute."

      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for more than 5 minutes."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second for more than 2 minutes."

      # Resource Usage
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for more than 5 minutes."

      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% for more than 5 minutes."

      # Database Alerts
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections."

      - alert: DatabaseDown
        expr: up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database is down"
          description: "PostgreSQL database has been down for more than 1 minute."

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis cache has been down for more than 1 minute."

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value }}%."

      # Faucet-specific Alerts
      - alert: FaucetBalanceLow
        expr: sui_faucet_balance_sui < 1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Faucet balance is low"
          description: "Faucet wallet balance is {{ $value }} SUI."

      - alert: HighFaucetRequestRate
        expr: rate(faucet_requests_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High faucet request rate"
          description: "Faucet is receiving {{ $value }} requests per second."

      - alert: FaucetRequestFailures
        expr: rate(faucet_requests_total{status="failed"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Faucet request failures detected"
          description: "Faucet is failing {{ $value }} requests per second."
