# Prometheus Configuration for Sui Faucet Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Sui Faucet Application
  - job_name: 'sui-faucet'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (if running)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # PostgreSQL Exporter (if running)
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis Exporter (if running)
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # cAdvisor for container metrics (if running)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
